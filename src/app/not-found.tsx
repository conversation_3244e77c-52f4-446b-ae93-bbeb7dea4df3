'use client';

import { Button } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { motion } from 'framer-motion';
import { BookOpen, Home, Search, Sparkles, Zap } from 'lucide-react';
import Link from 'next/link';
import { ReactNode, useEffect, useState } from 'react';

const floatingWords = [
	'404',
	'Lost',
	'Missing',
	'Oops',
	'Error',
	'NotFound',
	'Vocab',
	'Learn',
	'Study',
	'Words',
];

// Pre-calculate random values to avoid hydration mismatch
const getRandomPositions = (words: string[]) => {
	return words.map(() => ({
		left: Math.random() * 100,
		xOffset: Math.random() * 200 - 100,
		xAnimate: Math.random() * 400 - 200,
		rotation: Math.random() * 360,
		repeatDelay: Math.random() * 5,
	}));
};

const FloatingWord = ({
	word,
	delay,
	position,
}: {
	word: string;
	delay: number;
	position: {
		left: number;
		xOffset: number;
		xAnimate: number;
		rotation: number;
		repeatDelay: number;
	};
}) => {
	return (
		<motion.div
			initial={{ opacity: 0, y: 100, x: position.xOffset }}
			animate={{
				opacity: [0, 0.3, 0],
				y: [-100, -200, -300],
				x: position.xAnimate,
				rotate: position.rotation,
			}}
			transition={{
				duration: 8,
				delay,
				repeat: Infinity,
				repeatDelay: position.repeatDelay,
			}}
			className="absolute text-muted-foreground/20 font-mono text-sm pointer-events-none select-none"
			style={{
				left: `${position.left}%`,
				top: '100%',
			}}
		>
			{word}
		</motion.div>
	);
};

const GlitchText = ({ children }: { children: ReactNode }) => {
	const [isGlitching, setIsGlitching] = useState(false);

	useEffect(() => {
		const interval = setInterval(() => {
			setIsGlitching(true);
			setTimeout(() => setIsGlitching(false), 200);
		}, 3000);

		return () => clearInterval(interval);
	}, []);

	return (
		<motion.div
			className={`relative inline-block ${isGlitching ? 'animate-pulse' : ''}`}
			animate={
				isGlitching
					? {
							x: [0, -2, 2, -1, 1, 0],
							filter: [
								'hue-rotate(0deg)',
								'hue-rotate(90deg)',
								'hue-rotate(180deg)',
								'hue-rotate(270deg)',
								'hue-rotate(0deg)',
							],
					  }
					: {}
			}
			transition={{ duration: 0.2 }}
		>
			{children}
			{isGlitching && (
				<>
					<span className="absolute inset-0 text-red-500 opacity-70 -translate-x-1">
						{children}
					</span>
					<span className="absolute inset-0 text-blue-500 opacity-70 translate-x-1">
						{children}
					</span>
				</>
			)}
		</motion.div>
	);
};

export default function NotFound() {
	if (typeof window === 'undefined') return null;

	// Pre-calculate random positions on component mount
	const [randomPositions] = useState(() => getRandomPositions(floatingWords));
	const { t } = useTranslation();

	return (
		<div className="min-h-screen flex items-center justify-center relative overflow-hidden">
			{/* Animated Background */}
			<div className="fixed inset-0 -z-10">
				<div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-background to-destructive/10 animate-gradient-x" />
				<motion.div
					className="absolute inset-0 opacity-30"
					animate={{
						backgroundPosition: ['0% 0%', '100% 100%'],
					}}
					transition={{
						duration: 20,
						repeat: Infinity,
						repeatType: 'reverse',
					}}
					style={{
						backgroundImage:
							'radial-gradient(circle at 25% 25%, hsl(var(--primary)) 0%, transparent 50%), radial-gradient(circle at 75% 75%, hsl(var(--secondary)) 0%, transparent 50%)',
					}}
				/>
			</div>

			{/* Floating Words */}
			{floatingWords.map((word, index) => (
				<FloatingWord
					key={`${word}-${index}`}
					word={word}
					delay={index * 0.8}
					position={randomPositions[index]}
				/>
			))}

			{/* Main Content */}
			<motion.div
				initial={{ opacity: 0, scale: 0.8 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ duration: 0.8, ease: 'easeOut' }}
				className="text-center space-y-8 max-w-2xl mx-auto px-6 relative z-10"
			>
				{/* 404 Number with Glitch Effect */}
				<motion.div
					initial={{ y: -50 }}
					animate={{ y: 0 }}
					transition={{ delay: 0.2, type: 'spring', stiffness: 100 }}
				>
					<GlitchText>
						<h1 className="text-9xl md:text-[12rem] font-bold bg-gradient-to-r from-primary via-destructive to-primary bg-clip-text text-transparent font-mono">
							404
						</h1>
					</GlitchText>
				</motion.div>

				{/* Animated Icons */}
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 0.5 }}
					className="flex justify-center space-x-4 mb-6"
				>
					{[BookOpen, Search, Sparkles, Zap].map((Icon, index) => (
						<motion.div
							key={index}
							animate={{
								y: [0, -10, 0],
								rotate: [0, 5, -5, 0],
								scale: [1, 1.1, 1],
							}}
							transition={{
								duration: 2,
								delay: index * 0.2,
								repeat: Infinity,
								repeatType: 'reverse',
							}}
							className="text-primary/60"
						>
							<Icon size={32} />
						</motion.div>
					))}
				</motion.div>

				{/* Error Message */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.7 }}
					className="space-y-4"
				>
					<h2 className="text-3xl md:text-4xl font-bold text-foreground">
						{t('errors.404_title')}
					</h2>
					<p className="text-lg text-muted-foreground max-w-md mx-auto leading-relaxed">
						{t('errors.404_description')}
					</p>
				</motion.div>

				{/* Action Buttons */}
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ delay: 0.9 }}
					className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-6"
				>
					<Link href="/">
						<Button
							size="lg"
							className="group relative overflow-hidden bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg"
						>
							<motion.div
								className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
								animate={{ x: ['-100%', '100%'] }}
								transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
							/>
							<Home className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
							{t('errors.404_home_button')}
						</Button>
					</Link>

					<Link href="/collections">
						<Button
							variant="outline"
							size="lg"
							className="group px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg border-2 hover:border-primary/50"
						>
							<BookOpen className="mr-2 h-5 w-5 group-hover:rotate-12 transition-transform" />
							{t('errors.404_collections_button')}
						</Button>
					</Link>
				</motion.div>

				{/* Fun Fact */}
				<motion.div
					initial={{ opacity: 0 }}
					animate={{ opacity: 1 }}
					transition={{ delay: 1.2 }}
					className="mt-12 p-6 bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50"
				>
					<motion.div
						animate={{ rotate: [0, 5, -5, 0] }}
						transition={{ duration: 4, repeat: Infinity }}
						className="inline-block mb-2"
					>
						💡
					</motion.div>
					<p className="text-sm text-muted-foreground">
						<strong>{t('errors.404_fun_fact_prefix')}</strong>{' '}
						{t('errors.404_fun_fact')}
					</p>
				</motion.div>
			</motion.div>
		</div>
	);
}
